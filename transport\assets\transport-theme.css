/* نظام ألوان المواصلات البنفسجي */

:root {
    /* الألوان الأساسية البنفسجية */
    --purple-50: #faf5ff;
    --purple-100: #f3e8ff;
    --purple-200: #e9d5ff;
    --purple-300: #d8b4fe;
    --purple-400: #c084fc;
    --purple-500: #a855f7;
    --purple-600: #9333ea;
    --purple-700: #7e22ce;
    --purple-800: #6b21a8;
    --purple-900: #581c87;
    
    /* الألوان المتناسقة */
    --indigo-100: #e0e7ff;
    --indigo-200: #c7d2fe;
    --indigo-500: #6366f1;
    --indigo-800: #3730a3;
    
    --green-500: #10b981;
    --green-700: #047857;
    
    --blue-100: #dbeafe;
    --blue-500: #3b82f6;
    --blue-600: #2563eb;
    
    /* الألوان الرمادية */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
}

/* Progress Bar Styles */
.progress-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    max-width: 64rem;
    margin: 0 auto;
}

.progress-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    background-color: var(--gray-200);
    transform: translateY(-50%);
    z-index: 0;
}

.progress-step {
    position: relative;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.progress-circle {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.progress-circle.completed {
    background-color: var(--green-500);
    color: white;
}

.progress-circle.active {
    background-color: var(--purple-600);
    color: white;
}

.progress-circle.pending {
    background-color: var(--gray-200);
    color: var(--gray-600);
}

.progress-label {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
}

.progress-label.completed {
    color: var(--green-700);
}

.progress-label.active {
    color: var(--purple-700);
}

.progress-label.pending {
    color: var(--gray-500);
}

/* Button Styles */
.btn-purple {
    background-color: var(--purple-600);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-purple:hover {
    background-color: var(--purple-700);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(147, 51, 234, 0.3);
}

.btn-purple-outline {
    background-color: transparent;
    color: var(--purple-600);
    border: 2px solid var(--purple-600);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-purple-outline:hover {
    background-color: var(--purple-600);
    color: white;
}

/* Card Styles */
.transport-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--gray-100);
}

.transport-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.transport-card-header {
    height: 8px;
    background: linear-gradient(90deg, var(--purple-400), var(--purple-600));
}

/* Form Styles */
.form-input-purple {
    border: 1px solid var(--gray-300);
    border-radius: 0.375rem;
    padding: 0.75rem;
    width: 100%;
    transition: all 0.3s ease;
}

.form-input-purple:focus {
    outline: none;
    border-color: var(--purple-500);
    box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
}

/* Badge Styles */
.badge-purple {
    background-color: var(--purple-100);
    color: var(--purple-800);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.badge-green {
    background-color: var(--green-100);
    color: var(--green-800);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Icon Colors */
.icon-purple {
    color: var(--purple-500);
}

.icon-green {
    color: var(--green-500);
}

.icon-blue {
    color: var(--blue-500);
}

.icon-indigo {
    color: var(--indigo-500);
}

/* Background Colors */
.bg-purple-gradient {
    background: linear-gradient(135deg, var(--purple-600), var(--purple-800));
}

.bg-purple-light {
    background-color: var(--purple-50);
}

/* Text Colors */
.text-purple-primary {
    color: var(--purple-700);
}

.text-purple-secondary {
    color: var(--purple-600);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .progress-bar {
        flex-direction: column;
        gap: 1rem;
    }
    
    .progress-line {
        display: none;
    }
    
    .progress-circle {
        width: 2.5rem;
        height: 2.5rem;
    }
    
    .progress-label {
        font-size: 0.75rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--purple-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--purple-600);
}
