/* ملف CSS لتحسين الأداء والتجاوب المتقدم */

/* تحسين الخطوط */
@font-face {
    font-family: '<PERSON><PERSON>wal';
    font-display: swap;
    src: local('<PERSON><PERSON><PERSON>');
}

@font-face {
    font-family: 'Poppins';
    font-display: swap;
    src: local('Poppins');
}

/* تحسين التحميل */
.loading {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.loaded {
    opacity: 1;
}

/* تحسين الصور */
img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

img[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s ease;
}

img[loading="lazy"].loaded {
    opacity: 1;
}

/* تحسين الحركات للأجهزة المحمولة */
@media (max-width: 768px) {
    * {
        animation-duration: 0.2s !important;
        transition-duration: 0.2s !important;
    }
    
    .hover\:scale-110:hover,
    .hover-scale:hover {
        transform: scale(1.02) !important;
    }
    
    .hover\:-translate-y-2:hover {
        transform: translateY(-2px) !important;
    }
}

/* تحسين الأداء للشاشات الصغيرة */
@media (max-width: 480px) {
    /* تقليل الظلال */
    .shadow-lg {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }
    
    .shadow-xl {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
    }
    
    .shadow-2xl {
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1) !important;
    }
    
    /* تبسيط التدرجات */
    .hero-gradient {
        background: #8a2be2 !important;
    }
    
    /* تقليل border-radius */
    .rounded-xl {
        border-radius: 0.5rem !important;
    }
    
    .rounded-lg {
        border-radius: 0.375rem !important;
    }
}

/* تحسين التمرير */
html {
    scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }
}

/* تحسين الشبكة للأجهزة المختلفة */
.responsive-grid {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
}

@media (min-width: 640px) {
    .responsive-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .responsive-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
}

/* تحسين النصوص للقراءة */
.text-responsive {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
    line-height: 1.6;
}

.heading-responsive {
    font-size: clamp(1.25rem, 4vw, 2rem);
    line-height: 1.3;
}

.title-responsive {
    font-size: clamp(1.5rem, 5vw, 3rem);
    line-height: 1.2;
}

/* تحسين الأزرار للمس */
.btn-touch {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.btn-touch:active {
    transform: scale(0.98);
}

/* تحسين النماذج */
.form-responsive input,
.form-responsive select,
.form-responsive textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 16px; /* منع التكبير في iOS */
    transition: border-color 0.2s ease;
}

.form-responsive input:focus,
.form-responsive select:focus,
.form-responsive textarea:focus {
    outline: none;
    border-color: #8a2be2;
    box-shadow: 0 0 0 3px rgba(138, 43, 226, 0.1);
}

/* تحسين البطاقات */
.card-responsive {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.2s ease;
}

.card-responsive:hover {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .card-responsive {
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .card-responsive:hover {
        transform: translateY(-1px);
    }
}

/* تحسين القوائم المنسدلة */
.dropdown-responsive {
    position: relative;
}

.dropdown-responsive .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 50;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.dropdown-responsive:hover .dropdown-menu,
.dropdown-responsive.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

@media (max-width: 768px) {
    .dropdown-responsive .dropdown-menu {
        position: fixed;
        top: 50% !important;
        left: 50% !important;
        right: auto !important;
        transform: translate(-50%, -50%) !important;
        width: 90vw;
        max-width: 300px;
    }
    
    .dropdown-responsive:hover .dropdown-menu,
    .dropdown-responsive.active .dropdown-menu {
        transform: translate(-50%, -50%) !important;
    }
}

/* تحسين التنقل */
.nav-responsive {
    display: flex;
    align-items: center;
    gap: 1rem;
}

@media (max-width: 768px) {
    .nav-responsive {
        flex-direction: column;
        gap: 0;
    }
    
    .nav-responsive a {
        display: block;
        width: 100%;
        padding: 1rem;
        text-align: center;
        border-bottom: 1px solid #f3f4f6;
    }
    
    .nav-responsive a:last-child {
        border-bottom: none;
    }
}

/* تحسين المحتوى المرن */
.flex-responsive {
    display: flex;
    gap: 1rem;
    align-items: center;
}

@media (max-width: 768px) {
    .flex-responsive {
        flex-direction: column;
        align-items: stretch;
    }
    
    .flex-responsive > * {
        width: 100%;
    }
}

/* تحسين الصور المتجاوبة */
.img-responsive {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 0.5rem;
}

.img-responsive.aspect-video {
    aspect-ratio: 16/9;
}

.img-responsive.aspect-square {
    aspect-ratio: 1/1;
}

.img-responsive.aspect-portrait {
    aspect-ratio: 3/4;
}

/* تحسين المسافات */
.spacing-responsive {
    padding: clamp(1rem, 4vw, 2rem);
}

.spacing-responsive-sm {
    padding: clamp(0.5rem, 2vw, 1rem);
}

.spacing-responsive-lg {
    padding: clamp(2rem, 6vw, 4rem);
}

/* تحسين الحاويات */
.container-responsive {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .container-responsive {
        padding: 0 1.5rem;
    }
}

@media (min-width: 1024px) {
    .container-responsive {
        padding: 0 2rem;
    }
}

/* تحسين الوصولية */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible:focus {
    outline: 2px solid #8a2be2;
    outline-offset: 2px;
}

/* تحسين الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break {
        page-break-after: always;
    }
    
    .print-avoid-break {
        page-break-inside: avoid;
    }
    
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a, a:visited {
        text-decoration: underline;
    }
    
    img {
        max-width: 100% !important;
        page-break-inside: avoid;
    }
}

/* تحسين الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .dark-mode-responsive {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .dark-mode-responsive .card-responsive {
        background-color: #374151;
        color: #f9fafb;
    }
    
    .dark-mode-responsive .dropdown-menu {
        background-color: #374151;
        color: #f9fafb;
    }
}

/* تحسين الحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* تحسين الكثافة العالية */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .img-responsive {
        image-rendering: -webkit-optimize-contrast;
    }
}

/* تحسين الاتجاه */
@media (orientation: landscape) and (max-height: 500px) {
    .hero-gradient {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }
    
    .spacing-responsive-lg {
        padding: 1rem !important;
    }
}

/* تحسين التباين العالي */
@media (prefers-contrast: high) {
    .card-responsive {
        border: 2px solid #000;
    }
    
    .btn-touch {
        border: 2px solid currentColor;
    }
    
    .form-responsive input,
    .form-responsive select,
    .form-responsive textarea {
        border: 2px solid #000;
    }
}
