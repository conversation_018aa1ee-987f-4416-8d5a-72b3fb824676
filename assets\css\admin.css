/* Admin Panel Styles */

/* Dashboard Cards */
.dashboard-card {
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.dashboard-card i {
    font-size: 3rem;
    margin-bottom: 10px;
}

.dashboard-card h3 {
    font-size: 2rem;
    margin-bottom: 5px;
}

.dashboard-card p {
    margin-bottom: 0;
    font-size: 1rem;
}

/* Admin Navigation */
.admin-nav .btn {
    transition: all 0.3s ease;
}

.admin-nav .btn:hover {
    transform: translateY(-3px);
}

.admin-nav .btn.active {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Forms */
.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Image Preview */
.img-preview {
    max-height: 200px;
    object-fit: cover;
    border-radius: 5px;
}

/* RTL Support */
html[dir="rtl"] .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

html[dir="rtl"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

html[dir="rtl"] .text-md-end {
    text-align: left !important;
}

html[dir="rtl"] .dropdown-menu-end {
    right: auto !important;
    left: 0 !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .dashboard-card {
        margin-bottom: 15px;
    }
    
    .dashboard-card i {
        font-size: 2.5rem;
    }
    
    .dashboard-card h3 {
        font-size: 1.5rem;
    }
}
