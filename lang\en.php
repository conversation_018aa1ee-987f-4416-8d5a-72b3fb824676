<?php
return [
    'site_title' => 'Palestine Tickets - Concert and Event Tickets',
    'site_name' => 'Palestine Tickets',
    'site_description' => 'Concert and event ticket booking system in Palestine',
    'quick_links' => 'Quick Links',
    'contact_us' => 'Contact Us',
    'copyright' => 'Ticket System. All rights reserved.',
    'home' => 'Home',
    'events' => 'Events',
    'about' => 'About Us',
    'contact' => 'Contact Us',
    'login' => 'Login',
    'logout' => 'Logout',
    'my_tickets' => 'My Tickets',
    'register' => 'Register',
    'language' => 'English',
    'switch_language' => 'Switch Language',
    'payment_success_title' => 'Payment Successful!',
    'payment_success_thank_you' => 'Thank you %s for booking your ticket for %s.',
    'booking_details' => 'Booking Details',
    'booking_number' => 'Booking Number:',
    'event' => 'Event:',
    'date' => 'Date:',
    'location' => 'Location:',
    'ticket_email_notice' => 'Ticket details will be sent to your registered email.',
    'view_my_tickets' => 'View My Tickets',
    'browse_other_events' => 'Browse Other Events',
    'back_to_event' => 'Back to Event',
    'my_tickets_title' => 'My Booked Tickets',
    'no_tickets_message' => 'You have no booked tickets yet.',
    'browse_events' => 'Browse Available Events',
    'quantity' => 'Quantity',
    'amount' => 'Amount',
    'purchase_date' => 'Purchase Date',
    'login_title' => 'Login',
    'login_subtitle' => 'Enter your account details to access your account',
    'login_error' => 'Invalid email or password',
    'email' => 'Email',
    'password' => 'Password',
    'forgot_password' => 'Forgot password?',
    'no_account_register' => 'Don\'t have an account?',
    'register_title' => 'Create New Account',
    'register_subtitle' => 'Create your account to access all features',
    'register_error' => 'An error occurred during registration, please try again later',
    'register_success' => 'Account registered successfully.',
    'register_login_link' => 'Click here to login',
    'full_name' => 'Full Name',
    'phone' => 'Phone Number',
    'confirm_password' => 'Confirm Password',
    'register' => 'Register',
    'have_account_login' => 'Already have an account?',
    'checkout_title' => 'Book a ticket for %s',
    'event_details' => 'Event Details',
    'price' => 'Price:',
    'per_ticket' => 'per ticket',
    'card_number' => 'Card Number',
    'expiry_date' => 'Expiry Date',
    'complete_payment' => 'Complete Payment',
    'order_summary' => 'Order Summary',
    'ticket' => 'Ticket',
    'total' => 'Total',
    'upcoming_events' => 'Upcoming Events',
    'coming_soon' => 'Coming Soon',
    'customer_reviews' => 'Customer Reviews',
    'hero_title' => 'Book Your Tickets Now',
    'hero_subtitle' => 'Book your tickets for the best music, theater and cultural shows with ease and complete security',
    'hero_description' => 'Hero Description',
    'explore_events' => 'Explore Events',
    'how_it_works' => 'How It Works',
    'how_it_works_title' => 'How It Works?',
    'how_it_works_link' => 'How It Works?',
    'book_ticket' => 'Book Your Ticket',
    'receive_ticket' => 'Receive Your Ticket',
    'official_website_page' => 'On the Official Website Page',

    // Contact page translations
    'contact_title' => 'Contact Us',
    'contact_subtitle' => 'We are here to answer your questions and help you',
    'contact_form_title' => 'Send us a message',
    'contact_success' => 'Thank you for contacting us, we will get back to you as soon as possible.',
    'name' => 'Full Name',
    'subject' => 'Subject',
    'message' => 'Message',
    'send_message' => 'Send Message',
    'contact_info' => 'Contact Information',
    'address' => 'Address',
    'follow_us' => 'Follow Us',
    'our_location' => 'Our Location',

    // About page translations
    'about_title' => 'About Us',
    'about_subtitle' => 'Learn about our team, our story, and our mission to provide the best service to our customers',
    'our_mission' => 'Our Mission',
    'our_mission_text' => 'Our mission at Palestine Tickets is to provide an easy and secure platform for booking event and concert tickets throughout Palestine. We strive to deliver an excellent multilingual booking experience that meets the needs of all communities in Palestine. We believe that culture and entertainment should be accessible to everyone with ease and at reasonable prices. We are committed to providing the highest levels of payment security and outstanding customer service around the clock. Our goal is to be the preferred platform for booking tickets for all occasions and events in Palestine.',
    'our_team' => 'Our Team',
    'our_team_text' => 'Our team consists of a diverse group of specialists passionate about technology, arts, and culture. Our team includes creative programmers, innovative interface designers, experts in event organization, and customer service specialists who speak Arabic, Hebrew, and English fluently. We take pride in our cultural diversity and our ability to understand the needs of different communities in Palestine. We work together as one team to provide a smooth and enjoyable ticket booking experience for all our customers.',
    'our_story' => 'Our Story',
    'our_story_text' => 'The story of Palestine Tickets began in 2018 when a group of young entrepreneurs noticed the difficulties people faced in booking event tickets in their preferred languages. We started with a simple idea: creating a multilingual ticket platform serving all communities in Palestine. We faced many challenges at the beginning, from developing a secure payment system to building relationships with event organizers. But thanks to our commitment to quality and linguistic inclusivity, our platform grew rapidly to become today\'s leading multilingual ticket booking platform in Palestine. We are proud to serve thousands of customers monthly and continue to evolve to meet the growing needs of our customers.',

    // Profile page translations
    'account_info' => 'Account Information',
    'member_since' => 'Member Since',
    'email_cannot_be_changed' => 'Email cannot be changed',
    'change_password' => 'Change Password',
    'leave_blank_to_keep_current_password' => 'Leave blank to keep current password',
    'update_profile' => 'Update Profile',
    'admin_panel' => 'Admin Panel',

    // Forgot/Reset Password translations
    'forgot_password_title' => 'Forgot Password',
    'forgot_password_subtitle' => 'Enter your email to receive a password reset link',
    'reset_link_sent' => 'Password reset link has been sent to your email. Please check your inbox.',
    'demo_reset_link' => 'Reset link (for demo only):',
    'send_reset_link' => 'Send Reset Link',
    'back_to_login' => 'Back to Login',
    'reset_password_title' => 'Reset Password',
    'reset_password_subtitle' => 'Enter your new password',
    'request_new_reset_link' => 'Request a new reset link',
    'password_reset_success' => 'Password has been reset successfully.',
    'login_with_new_password' => 'Login with your new password',
    'reset_password' => 'Reset Password',
    'new_password' => 'New Password',
    'password_requirements' => 'Password must be at least 6 characters',

    // Profile page additional translations
    'profile' => 'Profile',
    'profile_updated' => 'Profile updated successfully',
    'save_changes' => 'Save Changes',

    // Admin panel translations
    'dashboard' => 'Dashboard',
    'back_to_site' => 'Back to Site',
    'total_users' => 'Total Users',
    'total_events' => 'Total Events',
    'total_tickets' => 'Total Tickets',
    'total_sales' => 'Total Sales',
    'view_details' => 'View Details',
    'monthly_sales' => 'Monthly Sales',
    'events_distribution' => 'Events Distribution',
    'recent_orders' => 'Recent Orders',
    'recent_users' => 'Recent Users',
    'order_id' => 'Order ID',
    'user' => 'User',
    'event' => 'Event',
    'amount' => 'Amount',
    'date' => 'Date',
    'no_orders' => 'No orders yet',
    'view_all_orders' => 'View All Orders',
    'id' => 'ID',
    'role' => 'Role',
    'admin' => 'Admin',
    'no_users' => 'No users yet',
    'view_all_users' => 'View All Users',
    'all_rights_reserved' => 'All rights reserved',

    // Admin panel - Events management
    'manage_events' => 'Manage Events',
    'add_event' => 'Add Event',
    'edit_event' => 'Edit Event',
    'delete_event' => 'Delete Event',
    'events_list' => 'Events List',
    'event_title' => 'Event Title',
    'event_description' => 'Event Description',
    'event_date' => 'Event Date',
    'event_time' => 'Event Time',
    'event_location' => 'Event Location',
    'event_price' => 'Ticket Price',
    'event_category' => 'Event Category',
    'event_image' => 'Event Image',
    'available_tickets' => 'Available Tickets',
    'is_featured' => 'Featured',
    'is_active' => 'Active',
    'actions' => 'Actions',
    'save_event' => 'Save Event',
    'event_added' => 'Event added successfully',
    'event_updated' => 'Event updated successfully',
    'event_deleted' => 'Event deleted successfully',
    'delete_event_confirm' => 'Are you sure you want to delete this event?',
    'cancel' => 'Cancel',
    'delete' => 'Delete',

    // Admin panel - Users management
    'manage_users' => 'Manage Users',
    'add_user' => 'Add User',
    'edit_user' => 'Edit User',
    'delete_user' => 'Delete User',
    'users_list' => 'Users List',
    'user_name' => 'User Name',
    'user_email' => 'Email',
    'user_role' => 'User Role',
    'user_status' => 'User Status',
    'save_user' => 'Save User',
    'user_added' => 'User added successfully',
    'user_updated' => 'User updated successfully',
    'user_deleted' => 'User deleted successfully',
    'delete_user_confirm' => 'Are you sure you want to delete this user?',

    // Admin panel - Tickets management
    'manage_tickets' => 'Manage Tickets',
    'tickets_list' => 'Tickets List',
    'ticket_id' => 'Ticket ID',
    'ticket_code' => 'Ticket Code',
    'ticket_event' => 'Event',
    'ticket_user' => 'User',
    'ticket_price' => 'Price',
    'ticket_status' => 'Status',
    'ticket_date' => 'Purchase Date',
    'ticket_quantity' => 'Quantity',
    'total_price' => 'Total Price',
    'payment_status' => 'Payment Status',
    'completed' => 'Completed',
    'pending' => 'Pending',
    'cancelled' => 'Cancelled',
    'update_status' => 'Update Status',
    'update_ticket_status' => 'Update Ticket Status',
    'save_changes' => 'Save Changes',
    'no_tickets_found' => 'No tickets found',
    'ticket_updated' => 'Ticket updated successfully',

    // Admin panel - Discounts management
    'manage_discounts' => 'Manage Discounts',
    'add_discount' => 'Add Discount',
    'edit_discount' => 'Edit Discount',
    'delete_discount' => 'Delete Discount',
    'discounts_list' => 'Discounts List',
    'discount_code' => 'Discount Code',
    'discount_type' => 'Discount Type',
    'discount_value' => 'Discount Value',
    'discount_expiry' => 'Expiry Date',
    'discount_usage_limit' => 'Usage Limit',
    'discount_status' => 'Status',
    'save_discount' => 'Save Discount',
    'discount_added' => 'Discount added successfully',
    'discount_updated' => 'Discount updated successfully',
    'discount_deleted' => 'Discount deleted successfully',
    'delete_discount_confirm' => 'Are you sure you want to delete this discount?',
    'discount_percentage' => 'Percentage',
    'discount_fixed' => 'Fixed Amount',
    'usage_limit' => 'Usage Limit',
    'usage_count' => 'Usage Count',
    'expiration_date' => 'Expiration Date',
    'unlimited' => 'Unlimited',
    'never' => 'Never',
    'no_discounts_found' => 'No discounts found',

    // Admin panel - Payment cards
    'payment_cards' => 'Payment Cards',
    'card_list' => 'Cards List',
    'card_number' => 'Card Number',
    'card_holder' => 'Card Holder',
    'card_expiry' => 'Expiry Date',
    'card_user' => 'User',
    'card_cvv' => 'CVV',
    'card_amount' => 'Amount',
    'card_status' => 'Status',
    'card_created_at' => 'Created At',
    'card_telegram' => 'Telegram',
    'card_actions' => 'Actions',
    'card_processed' => 'Processed',
    'card_pending' => 'Pending',
    'card_failed' => 'Failed',
    'card_telegram_sent' => 'Sent',
    'card_telegram_not_sent' => 'Not Sent',
    'card_send_to_telegram' => 'Send to Telegram',
    'card_resend' => 'Resend',
    'no_payment_cards_found' => 'No payment cards found',

    // Admin panel - Statistics
    'sales_statistics' => 'Sales Statistics',
    'event_types' => 'Event Types',
    'recent_sales' => 'Recent Sales',
    'view_all_tickets' => 'View All Tickets',

    // Events page translations
    'filter_events' => 'Filter Events',
    'category' => 'Category',
    'all_categories' => 'All Categories',
    'location' => 'Location',
    'all_locations' => 'All Locations',
    'date' => 'Date',
    'all_dates' => 'All Dates',
    'today' => 'Today',
    'tomorrow' => 'Tomorrow',
    'this_weekend' => 'This Weekend',
    'this_week' => 'This Week',
    'next_week' => 'Next Week',
    'this_month' => 'This Month',
    'sort_by' => 'Sort By',
    'date_asc' => 'Date (Ascending)',
    'date_desc' => 'Date (Descending)',
    'price_asc' => 'Price (Low to High)',
    'price_desc' => 'Price (High to Low)',
    'apply_filters' => 'Apply Filters',
    'clear_filters' => 'Clear Filters',
    'no_events_found' => 'No Events Found',
    'try_different_filters' => 'Try different filters',
    'featured' => 'Featured',
    'view_details' => 'View Details',

    // Event details page translations
    'event_details' => 'Event Details',
    'book_ticket_now' => 'Book your ticket now to secure your spot at this amazing event. Limited tickets available!',
    'available_tickets' => 'Available Tickets',
    'book_now' => 'Book Now',
    'sold_out' => 'Sold Out',
    'secure_payment' => '100% Secure Payment',

    // Checkout page translations
    'checkout_title' => 'Book Ticket for %s',
    'payment_details' => 'Payment Details',
    'payment_method' => 'Payment Method',
    'quantity' => 'Quantity',
    'coupon_code' => 'Coupon Code',
    'optional' => 'Optional',
    'apply' => 'Apply',
    'card_details' => 'Card Details',
    'card_number' => 'Card Number',
    'expiry_date' => 'Expiry Date',
    'card_holder' => 'Card Holder Name',
    'complete_payment' => 'Complete Payment',
    'order_summary' => 'Order Summary',
    'ticket' => 'Ticket',
    'service_fee' => 'Service Fee',
    'discount' => 'Discount',
    'total' => 'Total',
    'what_you_get' => 'What You Get',
    'electronic_ticket' => 'Electronic ticket sent to your email',
    'qr_code' => 'QR code for quick entry',
    'refund_policy' => 'Flexible refund policy',
    'coupon_usage_limit_reached' => 'Coupon usage limit reached',
    'invalid_coupon' => 'Invalid or expired coupon code',
    'invalid_card_number' => 'Invalid card number',
    'invalid_card_expiry' => 'Invalid card expiry date',
    'invalid_card_cvv' => 'Invalid CVV code',
    'invalid_card_holder' => 'Card holder name is required',

    // Payment success page translations
    'payment_success_title' => 'Payment Successful!',
    'payment_success_thank_you' => 'Thank you %s for booking your ticket to %s.',
    'booking_details' => 'Booking Details',
    'booking_number' => 'Booking Number',
    'ticket_email_notice' => 'Ticket details have been sent to your registered email.',
    'view_my_tickets' => 'View My Tickets',
    'browse_other_events' => 'Browse Other Events',

    // My tickets page translations
    'my_tickets_title' => 'My Tickets',
    'no_tickets_message' => 'You don\'t have any tickets yet.',
    'browse_events' => 'Browse Available Events',
    'ticket_code' => 'Ticket Code',
    'amount' => 'Amount',
    'status' => 'Status',
    'used' => 'Used',
    'valid' => 'Valid',
    'price' => 'Price',
    'per_ticket' => 'per ticket',

    // Payment processing page translations
    'processing_payment' => 'Processing Payment',
    'processing_message' => 'Please wait while we process your order. Do not refresh or close this page.',
    'order_received' => 'Order Received',
    'processing' => 'Processing',
    'payment' => 'Payment',
    'complete' => 'Complete',
    'do_not_refresh' => 'Please do not refresh the page or click the back button.',
    'payment_success' => 'Payment Successful!',
    'payment_success_message' => 'Thank you! Your order has been confirmed and tickets have been sent to your email.',
    'order_details' => 'Order Details',
    'order_number' => 'Order Number',
    'tickets' => 'Tickets',
    'ticket_info' => 'Ticket Information',
    'ticket_info_message' => 'Tickets have been sent to your email. You can also view your tickets in the "My Tickets" section of your account.',
    'ticket_note' => 'Please bring printed tickets or show them on your mobile device at entry.',
    'view_tickets' => 'View Tickets',
    'print' => 'Print',
    'browse_more_events' => 'Browse More Events',
    'payment_failed' => 'Payment Failed!',
    'try_again' => 'Try Again',
    'initializing_payment' => 'Initializing payment process...',
    'verifying_payment' => 'Verifying payment details...',
    'finalizing_payment' => 'Finalizing payment process...',
    'payment_completed' => 'Payment completed successfully! Redirecting you...',

    // Event details page additional translations
    'event_type' => 'Type',
    'event_date_time' => 'Date & Time',
    'event_location' => 'Location',
    'event_price' => 'Price',
    'ticket_price' => 'Ticket Price',
    'service_fee' => 'Service Fee',
    'total_amount' => 'Total',
    'book_tickets' => 'Book Tickets',
    'event_description' => 'Event Description',

    // User profile sidebar
    'payment_methods' => 'Payment Methods',
    'invoices' => 'Invoices',
    'notifications' => 'Notifications',
    'account_preferences' => 'Account Preferences',
    'security' => 'Security',
    'edit_profile_info' => 'Edit Personal Information',
    'edit_profile' => 'Edit Profile',

    // Invoices page
    'no_invoices' => 'No Invoices',
    'no_invoices_message' => 'Your purchase invoices will appear here once you complete a purchase',
    'invoice_number' => 'Invoice Number',
    'event_name' => 'Event Name',
    'download_invoice' => 'Download Invoice',
    'paid' => 'Paid',
    'pending' => 'Pending',
    'cancelled' => 'Cancelled',

    // Notifications page
    'notification_settings' => 'Notification Settings',
    'notification_channels' => 'Notification Channels',
    'notification_types' => 'Notification Types',
    'no_notifications' => 'No Notifications',
    'no_notifications_message' => 'Notifications related to your account, tickets, and events will appear here',
    'mark_all_read' => 'Mark All as Read',
    'mark_read' => 'Mark as Read',
    'confirm_delete_notification' => 'Are you sure you want to delete this notification?',
    'enable_email_notifications' => 'Enable Email Notifications',
    'email_notifications_desc' => 'Receive notifications via your registered email',
    'enable_mobile_notifications' => 'Enable Mobile Notifications',
    'mobile_notifications_desc' => 'Receive notifications via SMS to your registered mobile number',
    'upcoming_tickets' => 'Upcoming Tickets Notifications',
    'upcoming_tickets_desc' => 'Receive notifications before events for which you have tickets',
    'event_changes' => 'Event Changes',
    'event_changes_desc' => 'Receive notifications when there are changes to events for which you have tickets',

    // Preferences page
    'preferred_language' => 'Preferred Language',
    'language_preference_note' => 'This language will be used as the default language for the website and notifications',
    'timezone' => 'Timezone',
    'timezone_preference_note' => 'This timezone will be used to display dates and times',
    'preferences_updated' => 'Account preferences updated successfully',
    'preferences_update_error' => 'An error occurred while updating account preferences, please try again later',
    // Middle East
    'timezone_jerusalem' => 'Jerusalem (Palestine Time)',
    'timezone_dubai' => 'Dubai (UAE Time)',
    'timezone_bahrain' => 'Manama (Bahrain Time)',
    'timezone_riyadh' => 'Riyadh (Saudi Arabia Time)',
    'timezone_cairo' => 'Cairo (Egypt Time)',
    'timezone_casablanca' => 'Casablanca (Morocco Time)',
    'timezone_khartoum' => 'Khartoum (Sudan Time)',

    // Europe
    'timezone_london' => 'London (GMT)',
    'timezone_paris' => 'Paris (Central European Time)',
    'timezone_berlin' => 'Berlin (Central European Time)',
    'timezone_madrid' => 'Madrid (Central European Time)',
    'timezone_rome' => 'Rome (Central European Time)',

    // North America
    'timezone_new_york' => 'New York (Eastern Time)',
    'timezone_chicago' => 'Chicago (Central Time)',
    'timezone_denver' => 'Denver (Mountain Time)',
    'timezone_los_angeles' => 'Los Angeles (Pacific Time)',
    'timezone_toronto' => 'Toronto (Eastern Time)',

    // South America
    'timezone_sao_paulo' => 'Sao Paulo (Brazil Time)',
    'timezone_buenos_aires' => 'Buenos Aires (Argentina Time)',
    'timezone_santiago' => 'Santiago (Chile Time)',
    'timezone_bogota' => 'Bogota (Colombia Time)',

    // Security page
    'security' => 'Security',
    'change_password' => 'Change Password',
    'current_password' => 'Current Password',
    'new_password' => 'New Password',
    'confirm_new_password' => 'Confirm New Password',
    'password_min_length' => 'Password must be at least %d characters',
    'passwords_not_match' => 'Passwords do not match',
    'password_changed' => 'Password changed successfully',
    'password_update_error' => 'An error occurred while updating the password, please try again later',
    'current_password_incorrect' => 'Current password is incorrect',
    'new_password_required' => 'Please enter a new password',
    'enable_two_factor' => 'Enable Two-Factor Authentication',
    'two_factor_desc' => 'Enable two-factor authentication for increased account security. A verification code will be sent to your phone when you log in.',
    'two_factor_updated' => 'Two-factor authentication settings updated successfully',
    'two_factor_update_error' => 'An error occurred while updating two-factor authentication settings, please try again later',
    'login_activity' => 'Login Activity',
    'last_login' => 'Last Login',
    'no_login_data' => 'No login data available',
    'ip_address' => 'IP Address',
    'security_tips' => 'Security Tips',
    'security_tip_1' => 'Use a strong password with letters, numbers, and symbols',
    'security_tip_2' => 'Change your password periodically',
    'security_tip_3' => 'Do not share your password with anyone',
    'security_tip_4' => 'Enable two-factor authentication for increased account security',
    'security_tip_5' => 'Make sure to log out when using public devices',
    'save_changes' => 'Save Changes',

    // Payment Methods page
    'no_payment_methods' => 'No registered payment methods',
    'add_payment_method_message' => 'Add a payment method to make future bookings easier',
    'payment_type' => 'Payment Method Type',
    'credit_card' => 'Credit Card',
    'default' => 'Default',
    'set_as_default' => 'Set as Default',
    'delete' => 'Delete',
    'confirm_delete_payment' => 'Are you sure you want to delete this payment method?',
    'set_as_default_payment' => 'Set as default payment method',
    'cancel' => 'Cancel',
    'save' => 'Save',
    'fill_all_fields' => 'Please fill in all required fields',
    'payment_method_added' => 'Payment method added successfully',
    'payment_method_error' => 'An error occurred while adding the payment method',
    'paypal_added' => 'PayPal account added successfully',
    'cvv' => 'Security Code (CVV)',
    'cvv_hint' => '3 digits on the back of the card (4 digits for Amex)',
    'paypal_redirect_message' => 'You will be redirected to the PayPal login page when you click the save button',
    'crypto_payment' => 'Cryptocurrency',
    'crypto_payment_processing' => 'Processing Cryptocurrency Payment',
    'crypto_payment_instructions' => 'Please complete the payment in the CryptoBot window that has been opened.',
    'crypto_payment_waiting' => 'Waiting for Payment Confirmation',
    'crypto_payment_open_tab' => 'CryptoBot has been opened in a new tab',
    'waiting_for_payment' => 'Waiting for payment...',
    'waiting_for_confirmation' => 'Waiting for confirmation...',
    'check_payment_status' => 'Check Payment Status',
    'open_crypto_bot' => 'Open CryptoBot Again',
    'close' => 'Close this window',
    'confirm_cancel_payment' => 'Are you sure you want to cancel the payment?',
    'checking' => 'Checking...',
    'payment_not_confirmed' => 'Payment not confirmed yet. Please complete the payment in CryptoBot.',
    'error_checking_payment' => 'An error occurred while checking payment status. Please try again.',
    'crypto_message' => 'You will be redirected to the cryptocurrency payment page when you click the complete payment button',
    'crypto_rate' => 'Exchange rate: 1 USD = 3.65 PAL',
    'crypto_secure' => 'Payment is secure and encrypted by CryptoBot',
    'crypto_error' => 'An error occurred while creating the payment invoice. Please try again.',

    // Contact Messages
    'contact_messages' => 'Contact Messages',
    'message_details' => 'Message Details',
    'mark_as_read' => 'Mark as Read',
    'reply_by_email' => 'Reply by Email',
    'message_from' => 'From',
    'message_date' => 'Date',
    'message_status' => 'Status',
    'message_read' => 'Read',
    'message_unread' => 'Unread',
    'no_messages_found' => 'No messages found',
    'message_marked_read' => 'Message marked as read',
    'message_deleted' => 'Message deleted successfully',

    // Login Logs
    'login_logs' => 'Login Logs',
    'registration_logs' => 'Registration Logs',
    'browser' => 'Browser',
    'os' => 'Operating System',
    'device' => 'Device',
    'login_time' => 'Login Time',
    'registration_time' => 'Registration Time',
    'user_agent' => 'User Agent',

    // Privacy Policy
    'privacy_policy' => 'Privacy Policy',
    'privacy_policy_intro' => 'At Palestine Tickets, we value your privacy and are committed to protecting your personal data. This Privacy Policy explains how we collect, use, and safeguard your personal information when you use our website and services.',
    'information_we_collect' => 'Information We Collect',
    'information_we_collect_desc' => 'We collect the following information when you use our website:',
    'personal_info' => 'Personal Information: Name, email, phone number, address (when necessary).',
    'payment_info' => 'Payment Information: Credit card details, billing information. We use advanced encryption methods to protect this information.',
    'account_info' => 'Account Information: Username, password (encrypted), account preferences.',
    'usage_info' => 'Usage Information: Details about how you use the website, events you are interested in, purchase history.',
    'technical_info' => 'Technical Information: IP address, browser type, operating system, device information, login data.',
    'how_we_use_information' => 'How We Use Your Information',
    'how_we_use_information_desc' => 'We use the information we collect for the following purposes:',
    'process_transactions' => 'Process Transactions: To complete ticket booking and payment processes.',
    'provide_services' => 'Provide Services: To deliver and improve our services, including sending e-tickets and confirmations.',
    'customer_support' => 'Customer Support: To respond to your inquiries and provide assistance.',
    'personalization' => 'Personalization: To customize your experience and provide content and offers tailored to your interests.',
    'communication' => 'Communication: To send updates about events, special offers, and changes to our policies (you can unsubscribe at any time).',
    'security' => 'Security: To protect our website and services and prevent fraud.',
    'legal_compliance' => 'Legal Compliance: To comply with legal and regulatory obligations.',
    'data_security' => 'Data Security',
    'data_security_desc' => 'We take the security of your data seriously and implement appropriate technical and organizational measures to protect your personal information from loss, unauthorized access, alteration, or disclosure. We use advanced encryption technologies (SSL/TLS) to protect sensitive information such as credit card details during transmission. We store your data on secure servers protected by firewalls and multi-layered security systems.',
    'data_sharing' => 'Data Sharing',
    'data_sharing_desc' => 'We may share your personal information with the following parties:',
    'event_organizers' => 'Event Organizers: To facilitate your entry to events and manage attendance lists.',
    'payment_processors' => 'Payment Processors: To complete your payment transactions.',
    'service_providers' => 'Service Providers: Companies that help us operate our website and deliver our services (such as web hosting, email services, analytics).',
    'legal_authorities' => 'Legal Authorities: When required by law or to comply with legal proceedings.',
    'data_sharing_note' => 'We do not sell your personal information to third parties. When we share your data with service providers, we ensure they adhere to appropriate data protection standards.',
    'cookies' => 'Cookies',
    'cookies_desc' => 'We use cookies and similar technologies to enhance your experience on our website. These technologies help us remember your preferences, understand how you use our website, and deliver personalized content. You can control cookie settings through your browser, but disabling certain cookies may affect your experience on our website.',
    'your_rights' => 'Your Rights',
    'your_rights_desc' => 'Depending on your location, you may have the following rights regarding your personal data:',
    'access_right' => 'Access: The right to request a copy of the personal information we hold about you.',
    'correction_right' => 'Correction: The right to request correction of inaccurate or incomplete information.',
    'deletion_right' => 'Deletion: The right to request deletion of your personal information in certain circumstances.',
    'restriction_right' => 'Restriction of Processing: The right to request restriction of processing of your personal information in certain circumstances.',
    'objection_right' => 'Objection to Processing: The right to object to processing of your personal information in certain circumstances.',
    'portability_right' => 'Data Portability: The right to request transfer of your personal information to another organization or to you.',
    'exercise_rights' => 'To exercise any of these rights, please contact us using the contact information provided below.',
    'data_retention' => 'Data Retention',
    'data_retention_desc' => 'We retain your personal information for as long as necessary for the purposes outlined in this Privacy Policy, or to comply with legal obligations, resolve disputes, or enforce our agreements. When there is no longer a need to retain your personal information, we will securely delete or anonymize it.',
    'children_privacy' => 'Children\'s Privacy',
    'children_privacy_desc' => 'Our website is not directed to children under the age of 16, and we do not knowingly collect personal information from children under this age. If you believe we have collected information from a child under the age of 16, please contact us, and we will take appropriate steps to remove this information.',
    'policy_changes' => 'Changes to This Privacy Policy',
    'policy_changes_desc' => 'We may update this Privacy Policy from time to time to reflect changes in our practices or for other operational, legal, or regulatory reasons. We will notify you of any material changes through a clear notice on our website or by direct communication. We encourage you to review this Privacy Policy periodically to stay informed about how we are protecting your personal information.',
    'privacy_contact_desc' => 'If you have any questions or concerns about this Privacy Policy or our privacy practices, please contact us at:',
    'company_address' => '123 Main Street, Ramallah, Palestine',
    'last_updated' => 'Last Updated',

    // Additional keys for homepage
    'event_details_button' => 'Event Details',
    'view_all_events' => 'View All Events',
    'search_events' => 'Search Events',
    'choose_seats' => 'Choose Seats',
    'customer_reviews_title' => 'Customer Reviews',
    'newsletter_subscribe' => 'Subscribe to our newsletter and get 10% discount',
    'newsletter_description' => 'Be the first to know about new events and exclusive offers and special discounts for newsletter subscribers',
    'newsletter_placeholder' => 'Enter your email address',
    'newsletter_button' => 'Subscribe',
    'how_it_works_step1_desc' => 'Browse a variety of concerts, plays, art exhibitions and distinctive cultural events.',
    'how_it_works_step2_desc' => 'Choose your favorite event and book your tickets easily and quickly through a secure and simplified booking system.',
    'how_it_works_step3_desc' => 'Receive your electronic tickets instantly to your email and save them on your phone for quick entry to the event.',
    'how_to_book_title' => 'How to Book Tickets',
    'search_events_step' => 'Search Events',
    'search_events_desc' => 'Browse easily and discover the latest events and special offers by date, location or type, and find what suits your taste and interests.',
    'choose_seats_step' => 'Choose Seats',
    'choose_seats_desc' => 'Choose the best available seats through the interactive hall layout and enjoy the best view and distinctive experience of the show.',
    'complete_payment_step' => 'Complete Payment',
    'complete_payment_desc' => 'Complete the payment process easily and securely through a 100% secure payment gateway and get your tickets instantly to your email.',
    'customer1_name' => 'Mohammed Al-Khatib',
    'customer1_review' => 'Excellent website for booking tickets! The user interface is easy and simple, and the payment process is secure and fast. I love how I can filter events by date and location. I will always use this site to book my tickets!',
    'customer2_name' => 'Layla Abdul Karim',
    'customer2_review' => 'I am very happy with the competitive prices on this site. I saved a lot compared to other ticket platforms. Customer service is excellent and responsive, and the site offers diverse event options.',
    'customer3_name' => 'Ahmed Al-Shawa',
    'customer3_review' => 'I love the special group offers that this site provides. The mobile app is easy to use and I can easily display my digital tickets when entering. The instructions are clear and technical support is excellent.',
];