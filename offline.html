<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير متصل - تذاكر فلسطين</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #8a2be2 0%, #4b0082 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 1rem;
        }
        
        .offline-container {
            max-width: 500px;
            width: 100%;
        }
        
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.8;
        }
        
        .offline-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 1rem;
            min-width: 200px;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: white;
            color: #8a2be2;
            border-color: white;
        }
        
        .btn-primary:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
        }
        
        .connection-status {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
            font-size: 0.9rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 0.5rem;
        }
        
        .status-offline {
            background: #ef4444;
        }
        
        .status-online {
            background: #10b981;
        }
        
        @media (max-width: 480px) {
            .offline-title {
                font-size: 1.5rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
            
            .offline-icon {
                font-size: 3rem;
            }
            
            .btn {
                min-width: 100%;
            }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="offline-container fade-in">
        <div class="offline-icon pulse">
            📡
        </div>
        
        <h1 class="offline-title">غير متصل بالإنترنت</h1>
        
        <p class="offline-message">
            يبدو أنك غير متصل بالإنترنت حالياً. يرجى التحقق من اتصالك بالشبكة والمحاولة مرة أخرى.
        </p>
        
        <div class="offline-actions">
            <button onclick="location.reload()" class="btn btn-primary">
                🔄 إعادة المحاولة
            </button>
            
            <button onclick="goHome()" class="btn">
                🏠 العودة للصفحة الرئيسية
            </button>
            
            <button onclick="showCachedPages()" class="btn">
                📄 عرض الصفحات المحفوظة
            </button>
        </div>
        
        <div class="connection-status">
            <span id="connection-text">حالة الاتصال: غير متصل</span>
            <span class="status-indicator status-offline" id="status-indicator"></span>
        </div>
    </div>

    <script>
        // مراقبة حالة الاتصال
        function updateConnectionStatus() {
            const isOnline = navigator.onLine;
            const statusText = document.getElementById('connection-text');
            const statusIndicator = document.getElementById('status-indicator');
            
            if (isOnline) {
                statusText.textContent = 'حالة الاتصال: متصل';
                statusIndicator.className = 'status-indicator status-online';
                
                // إعادة تحميل الصفحة تلقائياً عند عودة الاتصال
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                statusText.textContent = 'حالة الاتصال: غير متصل';
                statusIndicator.className = 'status-indicator status-offline';
            }
        }
        
        // العودة للصفحة الرئيسية
        function goHome() {
            if (navigator.onLine) {
                window.location.href = '/';
            } else {
                alert('يرجى التحقق من اتصالك بالإنترنت أولاً');
            }
        }
        
        // عرض الصفحات المحفوظة
        function showCachedPages() {
            const cachedPages = [
                { name: 'الصفحة الرئيسية', url: '/' },
                { name: 'الفعاليات', url: '/events.php' },
                { name: 'من نحن', url: '/about.php' },
                { name: 'اتصل بنا', url: '/contact.php' }
            ];
            
            let message = 'الصفحات المتاحة في وضع عدم الاتصال:\n\n';
            cachedPages.forEach((page, index) => {
                message += `${index + 1}. ${page.name}\n`;
            });
            
            alert(message);
        }
        
        // مراقبة تغيير حالة الاتصال
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // تحديث حالة الاتصال عند تحميل الصفحة
        updateConnectionStatus();
        
        // محاولة إعادة الاتصال كل 30 ثانية
        setInterval(() => {
            if (!navigator.onLine) {
                // محاولة ping بسيط
                fetch('/', { method: 'HEAD', mode: 'no-cors' })
                    .then(() => {
                        updateConnectionStatus();
                    })
                    .catch(() => {
                        // لا حاجة لفعل شيء، ما زلنا غير متصلين
                    });
            }
        }, 30000);
        
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', () => {
            // إضافة تأثيرات بصرية
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', () => {
                    button.style.transform = 'translateY(-2px) scale(1.02)';
                });
                
                button.addEventListener('mouseleave', () => {
                    button.style.transform = 'translateY(0) scale(1)';
                });
            });
            
            // إضافة اختصارات لوحة المفاتيح
            document.addEventListener('keydown', (e) => {
                if (e.key === 'r' || e.key === 'R') {
                    location.reload();
                } else if (e.key === 'h' || e.key === 'H') {
                    goHome();
                } else if (e.key === 'Escape') {
                    goHome();
                }
            });
        });
        
        // رسالة ترحيب
        console.log('صفحة عدم الاتصال - تذاكر فلسطين');
        console.log('اضغط R لإعادة المحاولة، H للعودة للصفحة الرئيسية');
    </script>
</body>
</html>
