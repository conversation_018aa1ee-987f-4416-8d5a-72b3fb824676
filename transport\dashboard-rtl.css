/* تحسينات إضافية للوحة التحكم باللغة العربية */

/* تحسين الخطوط العربية */
body {
    font-family: 'Segoe UI', '<PERSON><PERSON><PERSON>', 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
}

/* تحسين التنقل */
.nav-item {
    transition: all 0.3s ease;
}

.nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.nav-item.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-bottom: 3px solid white;
    font-weight: bold;
}

/* تحسين البطاقات */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* تحسين الجداول */
table {
    border-collapse: separate;
    border-spacing: 0;
}

table th {
    background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
    color: white;
    font-weight: 600;
    text-align: right;
    padding: 12px 16px;
}

table td {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
}

table tr:hover {
    background-color: #f9fafb;
}

/* تحسين الأزرار */
.btn-primary {
    background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5b21b6 0%, #4c1d95 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.4);
}

/* تحسين الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    transition: all 0.3s ease;
}

.stats-card:hover {
    border-color: #7c3aed;
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.1);
}

/* تحسين الأيقونات */
.icon-container {
    background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تحسين الحالات */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: #dcfce7;
    color: #166534;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-cancelled {
    background-color: #fee2e2;
    color: #991b1b;
}

/* تحسين أزرار تغيير الحالة */
.status-toggle-btn {
    transition: all 0.3s ease;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.status-toggle-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: rgba(124, 58, 237, 0.3);
}

.status-toggle-btn:active {
    transform: translateY(0);
}

.status-toggle-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.status-toggle-btn:hover::before {
    left: 100%;
}

/* أزرار حالة السائقين */
.driver-status-buttons {
    display: flex;
    gap: 4px;
    margin-bottom: 8px;
}

.driver-status-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.driver-status-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.driver-status-btn.active {
    border-color: #7c3aed;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

/* تحسين الرسوم المتحركة للحالات */
.status-change-animation {
    animation: statusPulse 0.6s ease-in-out;
}

@keyframes statusPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 4px rgba(124, 58, 237, 0.3);
    }
    100% {
        transform: scale(1);
    }
}

/* تحسين مؤشرات الحالة */
.status-indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-left: 6px;
    animation: statusBlink 2s infinite;
}

.status-indicator-dot.active {
    background-color: #10b981;
}

.status-indicator-dot.busy {
    background-color: #f59e0b;
}

.status-indicator-dot.offline {
    background-color: #ef4444;
}

@keyframes statusBlink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* تحسين النماذج */
.modal-content {
    border-radius: 16px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.form-input {
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px 16px;
    transition: all 0.3s ease;
}

.form-input:focus {
    border-color: #7c3aed;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
    outline: none;
}

/* تحسين الرسوم المتحركة */
.dashboard-section {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.dashboard-section.active {
    opacity: 1;
    transform: translateY(0);
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .nav-item {
        padding: 8px 12px;
        font-size: 14px;
    }
    
    .stats-card {
        padding: 16px;
    }
    
    table {
        font-size: 14px;
    }
    
    .modal-content {
        margin: 16px;
        max-width: calc(100% - 32px);
    }
}

/* تحسين التمرير */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* تحسين التركيز */
*:focus {
    outline: 2px solid #7c3aed;
    outline-offset: 2px;
}

/* تحسين النصوص */
.text-gradient {
    background: linear-gradient(135deg, #7c3aed 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسين الظلال */
.shadow-custom {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-custom-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
                0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* تحسين النماذج المنبثقة */
.modal-overlay {
    backdrop-filter: blur(4px);
    background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
    max-height: 90vh;
    overflow-y: auto;
}

/* تحسين الرسائل */
.alert-message {
    animation: slideInDown 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

@keyframes slideInDown {
    from {
        transform: translate(-50%, -100%);
        opacity: 0;
    }
    to {
        transform: translate(-50%, 0);
        opacity: 1;
    }
}

/* تحسين الأزرار */
.btn-action {
    transition: all 0.2s ease;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-edit:hover {
    background-color: rgba(124, 58, 237, 0.1);
}

.btn-delete:hover {
    background-color: rgba(239, 68, 68, 0.1);
}

/* تحسين الجداول */
.table-row-hover:hover {
    background-color: #f8fafc;
    transform: scale(1.001);
    transition: all 0.2s ease;
}

/* تحسين حقول الإدخال */
.form-field {
    transition: all 0.3s ease;
}

.form-field:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.15);
}

/* تحسين البطاقات */
.driver-card {
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.driver-card:hover {
    border-color: #7c3aed;
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.1);
    transform: translateY(-2px);
}

/* تحسين الحالات */
.status-indicator {
    position: relative;
    overflow: hidden;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.status-indicator:hover::before {
    left: 100%;
}

/* تحسين التحميل */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #7c3aed;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
